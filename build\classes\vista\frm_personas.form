<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <NonVisualComponents>
    <Container class="javax.swing.JPanel" name="jPanel1">

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <EmptySpace min="0" pref="100" max="32767" attributes="0"/>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <EmptySpace min="0" pref="100" max="32767" attributes="0"/>
          </Group>
        </DimensionLayout>
      </Layout>
    </Container>
  </NonVisualComponents>
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="3"/>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="false"/>
  </SyntheticProperties>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace max="-2" attributes="0"/>
              <Component id="tjp_principal" max="32767" attributes="0"/>
              <EmptySpace max="-2" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Component id="tjp_principal" alignment="0" max="32767" attributes="0"/>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Container class="javax.swing.JTabbedPane" name="tjp_principal">

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JTabbedPaneSupportLayout"/>
      <SubComponents>
        <Container class="javax.swing.JPanel" name="pa_empleados">
          <Constraints>
            <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.support.JTabbedPaneSupportLayout" value="org.netbeans.modules.form.compat2.layouts.support.JTabbedPaneSupportLayout$JTabbedPaneConstraintsDescription">
              <JTabbedPaneConstraints tabName="Empleados">
                <Property name="tabTitle" type="java.lang.String" value="Empleados"/>
              </JTabbedPaneConstraints>
            </Constraint>
          </Constraints>

          <Layout>
            <DimensionLayout dim="0">
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" alignment="1" attributes="0">
                      <EmptySpace pref="199" max="32767" attributes="0"/>
                      <Component id="lbl_titulo_empleados" min="-2" pref="366" max="-2" attributes="0"/>
                      <EmptySpace min="-2" pref="161" max="-2" attributes="0"/>
                  </Group>
                  <Group type="103" rootIndex="1" groupAlignment="0" attributes="0">
                      <Group type="102" attributes="0">
                          <EmptySpace max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="0" attributes="0">
                              <Group type="102" alignment="0" attributes="0">
                                  <EmptySpace min="-2" pref="32" max="-2" attributes="0"/>
                                  <Group type="103" groupAlignment="1" max="-2" attributes="0">
                                      <Group type="102" alignment="1" attributes="0">
                                          <Component id="lbl_apellidos_e" min="-2" pref="65" max="-2" attributes="0"/>
                                          <EmptySpace type="separate" max="-2" attributes="0"/>
                                          <Component id="txt_apellidos_e" max="32767" attributes="0"/>
                                      </Group>
                                      <Group type="102" alignment="1" attributes="0">
                                          <Component id="lbl_direccion_e" min="-2" pref="65" max="-2" attributes="0"/>
                                          <EmptySpace type="separate" max="-2" attributes="0"/>
                                          <Component id="txt_direccion_e" max="32767" attributes="0"/>
                                      </Group>
                                      <Group type="102" alignment="0" attributes="0">
                                          <Component id="lbl_telefono_e" min="-2" pref="65" max="-2" attributes="0"/>
                                          <EmptySpace type="separate" max="-2" attributes="0"/>
                                          <Component id="txt_telefono_e" max="32767" attributes="0"/>
                                      </Group>
                                      <Group type="102" alignment="0" attributes="0">
                                          <Component id="lbl_fn_e" min="-2" pref="65" max="-2" attributes="0"/>
                                          <EmptySpace type="separate" max="-2" attributes="0"/>
                                          <Group type="103" groupAlignment="0" attributes="0">
                                              <Component id="btn_guardar_e" alignment="0" min="-2" max="-2" attributes="0"/>
                                              <Component id="lbl_mensaje" alignment="0" max="32767" attributes="0"/>
                                              <Component id="txt_fn_e" alignment="0" max="32767" attributes="0"/>
                                          </Group>
                                      </Group>
                                      <Group type="102" alignment="1" attributes="0">
                                          <Group type="103" groupAlignment="0" attributes="0">
                                              <Component id="lbl_nombres_e" min="-2" pref="65" max="-2" attributes="0"/>
                                              <Component id="lbl_codigo_e" min="-2" pref="56" max="-2" attributes="0"/>
                                          </Group>
                                          <EmptySpace type="separate" max="-2" attributes="0"/>
                                          <Group type="103" groupAlignment="0" attributes="0">
                                              <Component id="txt_nombres_e" alignment="0" min="-2" pref="146" max="-2" attributes="0"/>
                                              <Component id="txt_codigo_e" alignment="0" min="-2" pref="89" max="-2" attributes="0"/>
                                          </Group>
                                      </Group>
                                  </Group>
                              </Group>
                              <Component id="jsp_empleados" alignment="0" max="32767" attributes="0"/>
                          </Group>
                          <EmptySpace max="-2" attributes="0"/>
                      </Group>
                  </Group>
              </Group>
            </DimensionLayout>
            <DimensionLayout dim="1">
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" alignment="0" attributes="0">
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="lbl_titulo_empleados" min="-2" max="-2" attributes="0"/>
                      <EmptySpace pref="356" max="32767" attributes="0"/>
                  </Group>
                  <Group type="103" rootIndex="1" groupAlignment="0" attributes="0">
                      <Group type="102" attributes="0">
                          <EmptySpace min="-2" pref="41" max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="lbl_codigo_e" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="txt_codigo_e" alignment="3" min="-2" max="-2" attributes="0"/>
                          </Group>
                          <EmptySpace max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="lbl_nombres_e" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="txt_nombres_e" alignment="3" min="-2" max="-2" attributes="0"/>
                          </Group>
                          <EmptySpace max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="lbl_apellidos_e" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="txt_apellidos_e" alignment="3" min="-2" max="-2" attributes="0"/>
                          </Group>
                          <EmptySpace max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="lbl_direccion_e" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="txt_direccion_e" alignment="3" min="-2" max="-2" attributes="0"/>
                          </Group>
                          <EmptySpace max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="lbl_telefono_e" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="txt_telefono_e" alignment="3" min="-2" max="-2" attributes="0"/>
                          </Group>
                          <EmptySpace max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="lbl_fn_e" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="txt_fn_e" alignment="3" min="-2" max="-2" attributes="0"/>
                          </Group>
                          <EmptySpace max="-2" attributes="0"/>
                          <Component id="lbl_mensaje" min="-2" max="-2" attributes="0"/>
                          <EmptySpace max="-2" attributes="0"/>
                          <Component id="btn_guardar_e" min="-2" max="-2" attributes="0"/>
                          <EmptySpace max="-2" attributes="0"/>
                          <Component id="jsp_empleados" pref="131" max="32767" attributes="0"/>
                          <EmptySpace min="-2" pref="3" max="-2" attributes="0"/>
                      </Group>
                  </Group>
              </Group>
            </DimensionLayout>
          </Layout>
          <SubComponents>
            <Component class="javax.swing.JLabel" name="lbl_titulo_empleados">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Segoe UI" size="24" style="1"/>
                </Property>
                <Property name="text" type="java.lang.String" value="Formulario Empleado"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JLabel" name="lbl_codigo_e">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Segoe UI" size="12" style="1"/>
                </Property>
                <Property name="text" type="java.lang.String" value="C&#xf3;digo:"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JTextField" name="txt_codigo_e">
              <Events>
                <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="txt_codigo_eActionPerformed"/>
              </Events>
            </Component>
            <Component class="javax.swing.JLabel" name="lbl_nombres_e">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Segoe UI" size="12" style="1"/>
                </Property>
                <Property name="text" type="java.lang.String" value="Nombres:"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JTextField" name="txt_nombres_e">
              <Events>
                <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="txt_nombres_eActionPerformed"/>
              </Events>
            </Component>
            <Component class="javax.swing.JLabel" name="lbl_apellidos_e">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Segoe UI" size="12" style="1"/>
                </Property>
                <Property name="text" type="java.lang.String" value="Apellidos:"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JTextField" name="txt_apellidos_e">
              <Events>
                <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="txt_apellidos_eActionPerformed"/>
              </Events>
            </Component>
            <Component class="javax.swing.JLabel" name="lbl_direccion_e">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Segoe UI" size="12" style="1"/>
                </Property>
                <Property name="text" type="java.lang.String" value="Direcci&#xf3;n:"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JTextField" name="txt_direccion_e">
              <Events>
                <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="txt_direccion_eActionPerformed"/>
              </Events>
            </Component>
            <Component class="javax.swing.JLabel" name="lbl_telefono_e">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Segoe UI" size="12" style="1"/>
                </Property>
                <Property name="text" type="java.lang.String" value="Tel&#xe9;fono"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JTextField" name="txt_telefono_e">
              <Events>
                <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="txt_telefono_eActionPerformed"/>
              </Events>
            </Component>
            <Component class="javax.swing.JTextField" name="txt_fn_e">
              <Events>
                <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="txt_fn_eActionPerformed"/>
              </Events>
            </Component>
            <Component class="javax.swing.JLabel" name="lbl_fn_e">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Segoe UI" size="12" style="1"/>
                </Property>
                <Property name="text" type="java.lang.String" value="Nacimiento"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JLabel" name="lbl_mensaje">
              <Properties>
                <Property name="text" type="java.lang.String" value="0"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JButton" name="btn_guardar_e">
              <Properties>
                <Property name="text" type="java.lang.String" value="Guardar"/>
              </Properties>
              <Events>
                <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btn_guardar_eActionPerformed"/>
              </Events>
            </Component>
            <Container class="javax.swing.JScrollPane" name="jsp_empleados">

              <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
              <SubComponents>
                <Component class="javax.swing.JTable" name="tbl_empleados">
                  <Properties>
                    <Property name="model" type="javax.swing.table.TableModel" editor="org.netbeans.modules.form.editors2.TableModelEditor">
                      <Table columnCount="4" rowCount="4">
                        <Column editable="true" title="Title 1" type="java.lang.Object"/>
                        <Column editable="true" title="Title 2" type="java.lang.Object"/>
                        <Column editable="true" title="Title 3" type="java.lang.Object"/>
                        <Column editable="true" title="Title 4" type="java.lang.Object"/>
                      </Table>
                    </Property>
                  </Properties>
                </Component>
              </SubComponents>
            </Container>
          </SubComponents>
        </Container>
        <Container class="javax.swing.JPanel" name="pa_clientes">
          <Constraints>
            <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.support.JTabbedPaneSupportLayout" value="org.netbeans.modules.form.compat2.layouts.support.JTabbedPaneSupportLayout$JTabbedPaneConstraintsDescription">
              <JTabbedPaneConstraints tabName="CLientes">
                <Property name="tabTitle" type="java.lang.String" value="CLientes"/>
              </JTabbedPaneConstraints>
            </Constraint>
          </Constraints>

          <Layout>
            <DimensionLayout dim="0">
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" alignment="1" attributes="0">
                      <EmptySpace pref="208" max="32767" attributes="0"/>
                      <Component id="lbl_titulo_clientes" min="-2" pref="366" max="-2" attributes="0"/>
                      <EmptySpace min="-2" pref="152" max="-2" attributes="0"/>
                  </Group>
                  <Group type="102" attributes="0">
                      <EmptySpace min="-2" pref="32" max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="1" max="-2" attributes="0">
                          <Group type="102" alignment="1" attributes="0">
                              <Component id="lbl_apellidos_c" min="-2" pref="65" max="-2" attributes="0"/>
                              <EmptySpace type="separate" max="-2" attributes="0"/>
                              <Component id="txt_apellidos_c" max="32767" attributes="0"/>
                          </Group>
                          <Group type="102" alignment="1" attributes="0">
                              <Component id="lbl_direccion_c" min="-2" pref="65" max="-2" attributes="0"/>
                              <EmptySpace type="separate" max="-2" attributes="0"/>
                              <Component id="txt_direccion_c" max="32767" attributes="0"/>
                          </Group>
                          <Group type="102" alignment="0" attributes="0">
                              <Component id="lbl_telefono_c" min="-2" pref="65" max="-2" attributes="0"/>
                              <EmptySpace type="separate" max="-2" attributes="0"/>
                              <Component id="txt_telefono_c" max="32767" attributes="0"/>
                          </Group>
                          <Group type="102" alignment="0" attributes="0">
                              <Component id="lbl_fn_c" min="-2" pref="65" max="-2" attributes="0"/>
                              <EmptySpace type="separate" max="-2" attributes="0"/>
                              <Group type="103" groupAlignment="0" attributes="0">
                                  <Component id="btn_guardar_c" min="-2" max="-2" attributes="0"/>
                                  <Component id="lbl_mensaje_c" max="32767" attributes="0"/>
                                  <Component id="txt_fn_c" max="32767" attributes="0"/>
                              </Group>
                          </Group>
                          <Group type="102" alignment="1" attributes="0">
                              <Group type="103" groupAlignment="0" attributes="0">
                                  <Group type="102" alignment="1" attributes="0">
                                      <Component id="lbl_nombres_c" min="-2" pref="65" max="-2" attributes="0"/>
                                      <EmptySpace type="separate" max="-2" attributes="0"/>
                                  </Group>
                                  <Group type="102" alignment="0" attributes="0">
                                      <Component id="lbl_nit_c" min="-2" pref="29" max="-2" attributes="0"/>
                                      <EmptySpace max="32767" attributes="0"/>
                                  </Group>
                              </Group>
                              <Group type="103" groupAlignment="0" attributes="0">
                                  <Component id="txt_nombres_c" min="-2" pref="146" max="-2" attributes="0"/>
                                  <Component id="txt_nit_c" min="-2" pref="89" max="-2" attributes="0"/>
                              </Group>
                          </Group>
                      </Group>
                      <EmptySpace max="32767" attributes="0"/>
                  </Group>
                  <Component id="jsp_clientes" alignment="0" max="32767" attributes="0"/>
              </Group>
            </DimensionLayout>
            <DimensionLayout dim="1">
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" alignment="0" attributes="0">
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="lbl_titulo_clientes" min="-2" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="3" attributes="0">
                          <Component id="lbl_nit_c" alignment="3" min="-2" max="-2" attributes="0"/>
                          <Component id="txt_nit_c" alignment="3" min="-2" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="3" attributes="0">
                          <Component id="lbl_nombres_c" alignment="3" min="-2" max="-2" attributes="0"/>
                          <Component id="txt_nombres_c" alignment="3" min="-2" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="3" attributes="0">
                          <Component id="lbl_apellidos_c" alignment="3" min="-2" max="-2" attributes="0"/>
                          <Component id="txt_apellidos_c" alignment="3" min="-2" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="3" attributes="0">
                          <Component id="lbl_direccion_c" alignment="3" min="-2" max="-2" attributes="0"/>
                          <Component id="txt_direccion_c" alignment="3" min="-2" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="3" attributes="0">
                          <Component id="lbl_telefono_c" alignment="3" min="-2" max="-2" attributes="0"/>
                          <Component id="txt_telefono_c" alignment="3" min="-2" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="3" attributes="0">
                          <Component id="lbl_fn_c" alignment="3" min="-2" max="-2" attributes="0"/>
                          <Component id="txt_fn_c" alignment="3" min="-2" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="lbl_mensaje_c" min="-2" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="btn_guardar_c" min="-2" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="jsp_clientes" pref="131" max="32767" attributes="0"/>
                  </Group>
              </Group>
            </DimensionLayout>
          </Layout>
          <SubComponents>
            <Component class="javax.swing.JLabel" name="lbl_titulo_clientes">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Segoe UI" size="24" style="1"/>
                </Property>
                <Property name="text" type="java.lang.String" value="Formulario Clientes"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JLabel" name="lbl_nit_c">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Segoe UI" size="12" style="1"/>
                </Property>
                <Property name="text" type="java.lang.String" value="Nit:"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JTextField" name="txt_nit_c">
              <Events>
                <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="txt_nit_cActionPerformed"/>
              </Events>
            </Component>
            <Component class="javax.swing.JLabel" name="lbl_nombres_c">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Segoe UI" size="12" style="1"/>
                </Property>
                <Property name="text" type="java.lang.String" value="Nombres:"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JTextField" name="txt_nombres_c">
              <Events>
                <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="txt_nombres_cActionPerformed"/>
              </Events>
            </Component>
            <Component class="javax.swing.JLabel" name="lbl_apellidos_c">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Segoe UI" size="12" style="1"/>
                </Property>
                <Property name="text" type="java.lang.String" value="Apellidos:"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JTextField" name="txt_apellidos_c">
              <Events>
                <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="txt_apellidos_cActionPerformed"/>
              </Events>
            </Component>
            <Component class="javax.swing.JLabel" name="lbl_direccion_c">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Segoe UI" size="12" style="1"/>
                </Property>
                <Property name="text" type="java.lang.String" value="Direcci&#xf3;n:"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JTextField" name="txt_direccion_c">
              <Events>
                <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="txt_direccion_cActionPerformed"/>
              </Events>
            </Component>
            <Component class="javax.swing.JLabel" name="lbl_telefono_c">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Segoe UI" size="12" style="1"/>
                </Property>
                <Property name="text" type="java.lang.String" value="Tel&#xe9;fono"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JTextField" name="txt_telefono_c">
              <Events>
                <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="txt_telefono_cActionPerformed"/>
              </Events>
            </Component>
            <Component class="javax.swing.JTextField" name="txt_fn_c">
              <Events>
                <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="txt_fn_cActionPerformed"/>
              </Events>
            </Component>
            <Component class="javax.swing.JLabel" name="lbl_fn_c">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Segoe UI" size="12" style="1"/>
                </Property>
                <Property name="text" type="java.lang.String" value="Nacimiento"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JLabel" name="lbl_mensaje_c">
              <Properties>
                <Property name="text" type="java.lang.String" value="0"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JButton" name="btn_guardar_c">
              <Properties>
                <Property name="text" type="java.lang.String" value="Guardar"/>
              </Properties>
              <Events>
                <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btn_guardar_cActionPerformed"/>
              </Events>
            </Component>
            <Container class="javax.swing.JScrollPane" name="jsp_clientes">

              <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
              <SubComponents>
                <Component class="javax.swing.JTable" name="tbl_clientes">
                  <Properties>
                    <Property name="model" type="javax.swing.table.TableModel" editor="org.netbeans.modules.form.editors2.TableModelEditor">
                      <Table columnCount="4" rowCount="4">
                        <Column editable="true" title="Title 1" type="java.lang.Object"/>
                        <Column editable="true" title="Title 2" type="java.lang.Object"/>
                        <Column editable="true" title="Title 3" type="java.lang.Object"/>
                        <Column editable="true" title="Title 4" type="java.lang.Object"/>
                      </Table>
                    </Property>
                  </Properties>
                </Component>
              </SubComponents>
            </Container>
          </SubComponents>
        </Container>
      </SubComponents>
    </Container>
  </SubComponents>
</Form>
