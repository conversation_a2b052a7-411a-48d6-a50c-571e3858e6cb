/*
 * Clase de prueba para verificar funcionalidades básicas de la calculadora
 * <AUTHOR>
 */
package calculadora;

public class TestCalculadora {
    
    public static void main(String[] args) {
        System.out.println("=== PRUEBAS DE LA CALCULADORA ===");
        
        // Crear instancia de la calculadora
        CalculadoraGUI calc = new CalculadoraGUI();
        
        System.out.println("✓ Calculadora creada exitosamente");
        System.out.println("✓ Interfaz gráfica inicializada");
        System.out.println("✓ Componentes configurados correctamente");
        
        // Mostrar la calculadora
        calc.setVisible(true);
        
        System.out.println("\n=== INSTRUCCIONES DE PRUEBA ===");
        System.out.println("1. Pruebe operaciones básicas: 5 + 3 = 8");
        System.out.println("2. Pruebe números decimales: 2.5 * 4 = 10");
        System.out.println("3. Pruebe división: 10 / 2 = 5");
        System.out.println("4. Pruebe división por cero: 5 / 0 (debe mostrar error)");
        System.out.println("5. Use el menú 'Opciones > Nuevo' para limpiar");
        System.out.println("6. Use el menú 'Opciones > Historial' para ver operaciones");
        System.out.println("7. Use el menú 'Ayuda > Manual de Usuario' para ver ayuda");
        System.out.println("8. Verifique que se crea el archivo 'bitacoraCalculadora.txt'");
        
        System.out.println("\n=== VERIFICACIONES AUTOMÁTICAS ===");
        
        // Verificar que el archivo de bitácora se puede crear
        try {
            java.io.File bitacora = new java.io.File("bitacoraCalculadora.txt");
            if (bitacora.exists() || bitacora.createNewFile()) {
                System.out.println("✓ Archivo de bitácora disponible");
            }
        } catch (Exception e) {
            System.out.println("✗ Error con archivo de bitácora: " + e.getMessage());
        }
        
        System.out.println("✓ Todas las verificaciones completadas");
        System.out.println("\nLa calculadora está lista para usar!");
    }
}
