/*
 * Calculadora GUI - Interfaz gráfica para calculadora básica
 * <AUTHOR>
 */
package calculadora;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class CalculadoraGUI extends JFrame implements ActionListener {
    
    // Componentes de la interfaz
    private JTextField txtDisplay;
    private JButton[] btnNumeros;
    private JButton btnSuma, btnResta, btnMultiplicacion, btnDivision;
    private JButton btnIgual, btnPunto, btnLimpiar;
    private JMenuBar menuBar;
    private JMenu menuOpciones, menuAyuda;
    private JMenuItem menuNuevo, menuHistorial, menuManual;
    
    // Variables para cálculos
    private double numero1 = 0;
    private double numero2 = 0;
    private String operador = "";
    private boolean nuevaOperacion = true;
    private String operacionCompleta = "";
    
    public CalculadoraGUI() {
        initComponents();
        setupLayout();
        setupEventListeners();
    }
    
    private void initComponents() {
        // Configuración de la ventana principal
        setTitle("Calculadora");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setResizable(false);
        
        // Crear el campo de texto para mostrar números
        txtDisplay = new JTextField("0");
        txtDisplay.setEditable(false);
        txtDisplay.setHorizontalAlignment(JTextField.RIGHT);
        txtDisplay.setFont(new Font("Arial", Font.BOLD, 18));
        txtDisplay.setPreferredSize(new Dimension(250, 40));
        
        // Crear botones numéricos
        btnNumeros = new JButton[10];
        for (int i = 0; i < 10; i++) {
            btnNumeros[i] = new JButton(String.valueOf(i));
            btnNumeros[i].setFont(new Font("Arial", Font.BOLD, 16));
            btnNumeros[i].setPreferredSize(new Dimension(60, 50));
        }
        
        // Crear botones de operaciones
        btnSuma = new JButton("+");
        btnResta = new JButton("-");
        btnMultiplicacion = new JButton("*");
        btnDivision = new JButton("/");
        btnIgual = new JButton("=");
        btnPunto = new JButton(".");
        btnLimpiar = new JButton("C");
        
        // Configurar botones de operaciones
        JButton[] operaciones = {btnSuma, btnResta, btnMultiplicacion, btnDivision, 
                                btnIgual, btnPunto, btnLimpiar};
        for (JButton btn : operaciones) {
            btn.setFont(new Font("Arial", Font.BOLD, 16));
            btn.setPreferredSize(new Dimension(60, 50));
        }
        
        // Crear menú
        menuBar = new JMenuBar();
        menuOpciones = new JMenu("Opciones");
        menuAyuda = new JMenu("Ayuda");
        
        menuNuevo = new JMenuItem("Nuevo");
        menuHistorial = new JMenuItem("Historial");
        menuManual = new JMenuItem("Manual de Usuario");
        
        menuOpciones.add(menuNuevo);
        menuOpciones.add(menuHistorial);
        menuAyuda.add(menuManual);
        
        menuBar.add(menuOpciones);
        menuBar.add(menuAyuda);
        
        setJMenuBar(menuBar);
    }
    
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // Panel superior para el display
        JPanel panelDisplay = new JPanel();
        panelDisplay.add(txtDisplay);
        add(panelDisplay, BorderLayout.NORTH);
        
        // Panel central para los botones
        JPanel panelBotones = new JPanel(new GridLayout(4, 4, 5, 5));
        panelBotones.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // Fila 1: C, /, *, -
        panelBotones.add(btnLimpiar);
        panelBotones.add(btnDivision);
        panelBotones.add(btnMultiplicacion);
        panelBotones.add(btnResta);

        // Fila 2: 7, 8, 9, +
        panelBotones.add(btnNumeros[7]);
        panelBotones.add(btnNumeros[8]);
        panelBotones.add(btnNumeros[9]);
        panelBotones.add(btnSuma);

        // Fila 3: 4, 5, 6, =
        panelBotones.add(btnNumeros[4]);
        panelBotones.add(btnNumeros[5]);
        panelBotones.add(btnNumeros[6]);
        panelBotones.add(btnIgual);

        // Fila 4: 1, 2, 3, 0
        panelBotones.add(btnNumeros[1]);
        panelBotones.add(btnNumeros[2]);
        panelBotones.add(btnNumeros[3]);
        panelBotones.add(btnNumeros[0]);

        // Panel inferior para el punto decimal
        JPanel panelInferior = new JPanel();
        panelInferior.add(btnPunto);
        
        add(panelBotones, BorderLayout.CENTER);
        add(panelInferior, BorderLayout.SOUTH);

        // Ajustar tamaño y centrar ventana
        pack();
        setLocationRelativeTo(null);
    }
    
    private void setupEventListeners() {
        // Listeners para botones numéricos
        for (int i = 0; i < 10; i++) {
            btnNumeros[i].addActionListener(this);
        }
        
        // Listeners para botones de operaciones
        btnSuma.addActionListener(this);
        btnResta.addActionListener(this);
        btnMultiplicacion.addActionListener(this);
        btnDivision.addActionListener(this);
        btnIgual.addActionListener(this);
        btnPunto.addActionListener(this);
        btnLimpiar.addActionListener(this);
        
        // Listeners para menú
        menuNuevo.addActionListener(this);
        menuHistorial.addActionListener(this);
        menuManual.addActionListener(this);
    }
    
    @Override
    public void actionPerformed(ActionEvent e) {
        String comando = e.getActionCommand();

        // Hacer beep cuando se presiona cualquier boton
        hacerBeep();

        // Manejar números
        if (comando.matches("[0-9]")) {
            if (nuevaOperacion) {
                operacionCompleta = comando;
                txtDisplay.setText(operacionCompleta);
                nuevaOperacion = false;
            } else {
                operacionCompleta += comando;
                txtDisplay.setText(operacionCompleta);
            }
        }
        // Manejar punto decimal
        else if (comando.equals(".")) {
            if (!operacionCompleta.endsWith(".") && !operacionCompleta.contains(".") ||
                (operador.length() > 0 && !operacionCompleta.substring(operacionCompleta.indexOf(operador) + 1).contains("."))) {
                operacionCompleta += ".";
                txtDisplay.setText(operacionCompleta);
            }
        }
        // Manejar operaciones
        else if (comando.matches("[+\\-*/]")) {
            if (!operacionCompleta.isEmpty() && !operacionCompleta.endsWith(" + ") &&
                !operacionCompleta.endsWith(" - ") && !operacionCompleta.endsWith(" x ") &&
                !operacionCompleta.endsWith(" / ")) {

                // Convertir * a x para mostrar
                String operadorDisplay = comando.equals("*") ? "x" : comando;
                operacionCompleta += " " + operadorDisplay + " ";
                txtDisplay.setText(operacionCompleta);
                operador = comando; // Guardar el operador real para cálculos
                nuevaOperacion = false;
            }
        }
        // Manejar igual
        else if (comando.equals("=")) {
            calcular();
        }
        // Manejar limpiar
        else if (comando.equals("C")) {
            limpiar();
        }
        // Manejar menú
        else if (comando.equals("Nuevo")) {
            limpiar();
            guardarEnBitacora("Nuevo");
        }
        else if (comando.equals("Historial")) {
            mostrarHistorial();
        }
        else if (comando.equals("Manual de Usuario")) {
            mostrarManual();
        }
    }
    
    private void calcular() {
        if (!operador.isEmpty() && !operacionCompleta.isEmpty()) {
            try {
                // Buscar el operador en la operación completa
                String[] partes = null;

                if (operacionCompleta.contains(" + ")) {
                    partes = operacionCompleta.split(" \\+ ");
                } else if (operacionCompleta.contains(" - ")) {
                    partes = operacionCompleta.split(" - ");
                } else if (operacionCompleta.contains(" x ")) {
                    partes = operacionCompleta.split(" x ");
                } else if (operacionCompleta.contains(" / ")) {
                    partes = operacionCompleta.split(" / ");
                }

                if (partes != null && partes.length == 2) {
                    numero1 = Double.parseDouble(partes[0].trim());
                    numero2 = Double.parseDouble(partes[1].trim());
                    double resultado = 0;

                    switch (operador) {
                        case "+":
                            resultado = numero1 + numero2;
                            break;
                        case "-":
                            resultado = numero1 - numero2;
                            break;
                        case "*":
                            resultado = numero1 * numero2;
                            break;
                        case "/":
                            if (numero2 != 0) {
                                resultado = numero1 / numero2;
                            } else {
                                JOptionPane.showMessageDialog(this, "Error: División por cero");
                                return;
                            }
                            break;
                    }

                    // Mostrar solo el resultado
                    String resultadoStr;
                    if (resultado == (int) resultado) {
                        resultadoStr = String.valueOf((int) resultado);
                    } else {
                        resultadoStr = String.valueOf(resultado);
                    }

                    txtDisplay.setText(resultadoStr);

                    // Guardar en bitácora
                    String operacion = numero1 + " " + operador + " " + numero2 + " = " + resultado;
                    guardarEnBitacora(operacion);

                    // Preparar para nueva operación
                    operador = "";
                    operacionCompleta = resultadoStr;
                    nuevaOperacion = true;
                }
            } catch (NumberFormatException ex) {
                JOptionPane.showMessageDialog(this, "Error en la operación");
                limpiar();
            }
        }
    }
    
    private void limpiar() {
        txtDisplay.setText("0");
        numero1 = 0;
        numero2 = 0;
        operador = "";
        operacionCompleta = "";
        nuevaOperacion = true;
    }
    
    private void guardarEnBitacora(String operacion) {
        try {
            FileWriter writer = new FileWriter("bitacoraCalculadora.txt", true);
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
            String fecha = sdf.format(new Date());
            writer.write(fecha + " - " + operacion + "\n");
            writer.close();
        } catch (IOException e) {
            System.err.println("Error al escribir en la bitácora: " + e.getMessage());
        }
    }
    
    private void mostrarHistorial() {
        // Crear ventana para mostrar historial
        JFrame ventanaHistorial = new JFrame("Historial de Operaciones");
        JTextArea textArea = new JTextArea(20, 50);
        textArea.setEditable(false);
        
        try {
            java.io.BufferedReader reader = new java.io.BufferedReader(
                new java.io.FileReader("bitacoraCalculadora.txt"));
            String linea;
            while ((linea = reader.readLine()) != null) {
                textArea.append(linea + "\n");
            }
            reader.close();
        } catch (IOException e) {
            textArea.setText("No se pudo cargar el historial.");
        }
        
        JScrollPane scrollPane = new JScrollPane(textArea);
        ventanaHistorial.add(scrollPane);
        ventanaHistorial.pack();
        ventanaHistorial.setLocationRelativeTo(this);
        ventanaHistorial.setVisible(true);
    }
    
    private void mostrarManual() {
        String manual = "MANUAL DE USUARIO - CALCULADORA\n\n" +
                       "1. Numeros: Haga clic en los botones numericos (0-9)\n" +
                       "2. Operaciones: Use +, -, *, / para operaciones basicas\n" +
                       "3. Punto decimal: Use el boton '.' para numeros decimales\n" +
                       "4. Igual: Presione '=' para obtener el resultado\n" +
                       "5. Limpiar: Use 'C' para limpiar la pantalla\n\n" +
                       "MENU:\n" +
                       "- Nuevo: Reinicia la calculadora\n" +
                       "- Historial: Muestra operaciones anteriores\n" +
                       "- Manual: Muestra esta ayuda";

        JOptionPane.showMessageDialog(this, manual, "Manual de Usuario",
                                    JOptionPane.INFORMATION_MESSAGE);
    }

    private void hacerBeep() {
        try {
            // Crear un sonido más suave usando frecuencias
            new Thread(() -> {
                try {
                    // Sonido más suave y corto
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    Thread.sleep(50); // Sonido muy corto
                } catch (Exception e) {
                    // Si no funciona, no hacer nada
                }
            }).start();
        } catch (Exception e) {
            // Si no se puede hacer sonido, no hacer nada
        }
    }
}
