package calculadora;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class <PERSON>culadoraG<PERSON> extends <PERSON><PERSON>rame implements ActionListener {
    
    private JTextField txtDisplay;
    private JButton[] btnNumeros;
    private JButton btnSuma, btnResta, btnMultiplicacion, btnDivision;
    private JButton btnIgual, btnPunto, btnLimpiar;
    private JMenuBar menuBar;
    private JMenu menuOpciones, menuAyuda;
    private JMenuItem menuNuevo, menuHistorial, menuManual;
    
    private double numero1 = 0;
    private double numero2 = 0;
    private String operador = "";
    private boolean nuevaOperacion = true;
    private String operacionCompleta = "";
    
    public CalculadoraGUI() {
        initComponents();
    }
    
    private void initComponents() {
        setTitle("Calculadora");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setResizable(false);
        setLayout(new BorderLayout());
        
        crearMenu();
        
        txtDisplay = new JTextField("0");
        txtDisplay.setEditable(false);
        txtDisplay.setHorizontalAlignment(JTextField.RIGHT);
        txtDisplay.setFont(new Font("Arial", Font.BOLD, 24));
        txtDisplay.setPreferredSize(new Dimension(300, 50));
        add(txtDisplay, BorderLayout.NORTH);
        
        crearBotones();
        
        JPanel panelBotones = new JPanel(new GridLayout(4, 4, 5, 5));
        panelBotones.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        panelBotones.add(btnLimpiar);
        panelBotones.add(btnDivision);
        panelBotones.add(btnMultiplicacion);
        panelBotones.add(btnResta);
        
        panelBotones.add(btnNumeros[7]);
        panelBotones.add(btnNumeros[8]);
        panelBotones.add(btnNumeros[9]);
        panelBotones.add(btnSuma);
        
        panelBotones.add(btnNumeros[4]);
        panelBotones.add(btnNumeros[5]);
        panelBotones.add(btnNumeros[6]);
        panelBotones.add(btnPunto);
        
        panelBotones.add(btnNumeros[1]);
        panelBotones.add(btnNumeros[2]);
        panelBotones.add(btnNumeros[3]);
        panelBotones.add(btnNumeros[0]);
        
        JPanel panelInferior = new JPanel();
        panelInferior.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        btnIgual.setPreferredSize(new Dimension(320, 40));
        panelInferior.add(btnIgual);
        
        add(panelBotones, BorderLayout.CENTER);
        add(panelInferior, BorderLayout.SOUTH);
        
        pack();
        setLocationRelativeTo(null);
    }
    
    private void crearMenu() {
        menuBar = new JMenuBar();
        
        menuOpciones = new JMenu("Opciones");
        menuAyuda = new JMenu("Ayuda");
        
        menuNuevo = new JMenuItem("Nuevo");
        menuHistorial = new JMenuItem("Historial");
        menuManual = new JMenuItem("Manual de Usuario");
        
        menuOpciones.add(menuNuevo);
        menuOpciones.add(menuHistorial);
        menuAyuda.add(menuManual);
        
        menuBar.add(menuOpciones);
        menuBar.add(menuAyuda);
        
        setJMenuBar(menuBar);
        
        menuNuevo.addActionListener(this);
        menuHistorial.addActionListener(this);
        menuManual.addActionListener(this);
    }
    
    private void crearBotones() {
        btnNumeros = new JButton[10];
        for (int i = 0; i < 10; i++) {
            btnNumeros[i] = new JButton(String.valueOf(i));
            btnNumeros[i].addActionListener(this);
            btnNumeros[i].setFont(new Font("Arial", Font.BOLD, 18));
        }
        
        btnSuma = new JButton("+");
        btnResta = new JButton("-");
        btnMultiplicacion = new JButton("*");
        btnDivision = new JButton("/");
        btnIgual = new JButton("=");
        btnPunto = new JButton(".");
        btnLimpiar = new JButton("C");
        
        JButton[] operaciones = {btnSuma, btnResta, btnMultiplicacion, btnDivision, 
                                btnIgual, btnPunto, btnLimpiar};
        
        for (JButton btn : operaciones) {
            btn.addActionListener(this);
            btn.setFont(new Font("Arial", Font.BOLD, 18));
        }
    }
    
    @Override
    public void actionPerformed(ActionEvent e) {
        String comando = e.getActionCommand();
        
        hacerBeep();
        
        if (comando.matches("[0-9]")) {
            if (nuevaOperacion) {
                operacionCompleta = comando;
                txtDisplay.setText(operacionCompleta);
                nuevaOperacion = false;
            } else {
                operacionCompleta += comando;
                txtDisplay.setText(operacionCompleta);
            }
        }
        else if (comando.equals(".")) {
            if (!operacionCompleta.endsWith(".") && !operacionCompleta.contains(".") || 
                (operador.length() > 0 && !operacionCompleta.substring(operacionCompleta.indexOf(operador) + 1).contains("."))) {
                operacionCompleta += ".";
                txtDisplay.setText(operacionCompleta);
            }
        }
        else if (comando.matches("[+\\-*/]")) {
            if (!operacionCompleta.isEmpty() && !operacionCompleta.endsWith(" + ") && 
                !operacionCompleta.endsWith(" - ") && !operacionCompleta.endsWith(" x ") && 
                !operacionCompleta.endsWith(" / ")) {
                
                String operadorDisplay = comando.equals("*") ? "x" : comando;
                operacionCompleta += " " + operadorDisplay + " ";
                txtDisplay.setText(operacionCompleta);
                operador = comando;
                nuevaOperacion = false;
            }
        }
        else if (comando.equals("=")) {
            calcular();
        }
        else if (comando.equals("C")) {
            limpiar();
        }
        else if (comando.equals("Nuevo")) {
            limpiar();
            guardarEnBitacora("Nuevo");
        }
        else if (comando.equals("Historial")) {
            mostrarHistorial();
        }
        else if (comando.equals("Manual de Usuario")) {
            mostrarManual();
        }
    }
    
    private void calcular() {
        if (!operador.isEmpty() && !operacionCompleta.isEmpty()) {
            try {
                String[] partes = null;
                
                if (operacionCompleta.contains(" + ")) {
                    partes = operacionCompleta.split(" \\+ ");
                } else if (operacionCompleta.contains(" - ")) {
                    partes = operacionCompleta.split(" - ");
                } else if (operacionCompleta.contains(" x ")) {
                    partes = operacionCompleta.split(" x ");
                } else if (operacionCompleta.contains(" / ")) {
                    partes = operacionCompleta.split(" / ");
                }
                
                if (partes != null && partes.length == 2) {
                    numero1 = Double.parseDouble(partes[0].trim());
                    numero2 = Double.parseDouble(partes[1].trim());
                    double resultado = 0;
                    
                    switch (operador) {
                        case "+":
                            resultado = numero1 + numero2;
                            break;
                        case "-":
                            resultado = numero1 - numero2;
                            break;
                        case "*":
                            resultado = numero1 * numero2;
                            break;
                        case "/":
                            if (numero2 != 0) {
                                resultado = numero1 / numero2;
                            } else {
                                JOptionPane.showMessageDialog(this, "Error: División por cero");
                                return;
                            }
                            break;
                    }
                    
                    String resultadoStr;
                    if (resultado == (int) resultado) {
                        resultadoStr = String.valueOf((int) resultado);
                    } else {
                        resultadoStr = String.valueOf(resultado);
                    }
                    
                    txtDisplay.setText(resultadoStr);
                    
                    String operacion = numero1 + " " + operador + " " + numero2 + " = " + resultado;
                    guardarEnBitacora(operacion);
                    
                    operador = "";
                    operacionCompleta = resultadoStr;
                    nuevaOperacion = true;
                }
            } catch (NumberFormatException ex) {
                JOptionPane.showMessageDialog(this, "Error en la operación");
                limpiar();
            }
        }
    }
    
    private void limpiar() {
        txtDisplay.setText("0");
        numero1 = 0;
        numero2 = 0;
        operador = "";
        operacionCompleta = "";
        nuevaOperacion = true;
    }
    
    private void guardarEnBitacora(String operacion) {
        try {
            FileWriter writer = new FileWriter("bitacoraCalculadora.txt", true);
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
            String fecha = sdf.format(new Date());
            writer.write(fecha + " - " + operacion + "\n");
            writer.close();
        } catch (IOException e) {
            System.err.println("Error al guardar en bitácora: " + e.getMessage());
        }
    }
    
    private void mostrarHistorial() {
        try {
            java.io.BufferedReader reader = new java.io.BufferedReader(new java.io.FileReader("bitacoraCalculadora.txt"));
            StringBuilder historial = new StringBuilder();
            String linea;
            
            while ((linea = reader.readLine()) != null) {
                historial.append(linea).append("\n");
            }
            reader.close();
            
            if (historial.length() > 0) {
                JTextArea textArea = new JTextArea(historial.toString());
                textArea.setEditable(false);
                JScrollPane scrollPane = new JScrollPane(textArea);
                scrollPane.setPreferredSize(new Dimension(400, 300));
                
                JOptionPane.showMessageDialog(this, scrollPane, "Historial de Operaciones", 
                                            JOptionPane.INFORMATION_MESSAGE);
            } else {
                JOptionPane.showMessageDialog(this, "No hay operaciones en el historial");
            }
        } catch (IOException e) {
            JOptionPane.showMessageDialog(this, "Error al leer el historial: " + e.getMessage());
        }
    }
    
    private void mostrarManual() {
        String manual = "MANUAL DE USUARIO - CALCULADORA\n\n" +
                       "1. Numeros: Haga clic en los botones numericos (0-9)\n" +
                       "2. Operaciones: Use +, -, *, / para operaciones basicas\n" +
                       "3. Punto decimal: Use el boton '.' para numeros decimales\n" +
                       "4. Igual: Presione '=' para obtener el resultado\n" +
                       "5. Limpiar: Use 'C' para limpiar la pantalla\n\n" +
                       "MENU:\n" +
                       "- Nuevo: Reinicia la calculadora\n" +
                       "- Historial: Muestra operaciones anteriores\n" +
                       "- Manual: Muestra esta ayuda";
        
        JOptionPane.showMessageDialog(this, manual, "Manual de Usuario", 
                                    JOptionPane.INFORMATION_MESSAGE);
    }
    
    private void hacerBeep() {
        try {
            new Thread(() -> {
                try {
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    Thread.sleep(50);
                } catch (Exception e) {
                }
            }).start();
        } catch (Exception e) {
        }
    }
}
