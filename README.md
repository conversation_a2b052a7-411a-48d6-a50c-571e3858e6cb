# Calculadora Java - Proyecto UMG

## Descripción
Esta es una calculadora básica desarrollada en Java con interfaz gráfica usando Swing. La calculadora permite realizar operaciones aritméticas básicas y mantiene un historial de operaciones en un archivo de bitácora.

## Características

### Funcionalidades Principales
- **Operaciones básicas**: Suma (+), Resta (-), Multiplicación (*), División (/)
- **Números decimales**: Soporte para números con punto decimal
- **Interfaz gráfica**: Diseño intuitivo con botones numéricos y de operaciones
- **Bitácora automática**: Todas las operaciones se guardan en `bitacoraCalculadora.txt`
- **Menú de opciones**: Nuevo, Historial y Manual de Usuario

### Componentes de la Interfaz
- **Campo de texto**: Muestra los números ingresados y resultados
- **Botones numéricos**: 0-9 para ingresar números
- **Botones de operaciones**: +, -, *, /, = para realizar cálculos
- **Botón punto decimal**: Para números decimales
- **Botón limpiar (C)**: Reinicia la calculadora (botón rojo)
- **Menú Opciones**: 
  - Nuevo: Reinicia la calculadora y registra la acción
  - Historial: Muestra ventana con todas las operaciones realizadas
- **Menú Ayuda**: Manual de Usuario con instrucciones

## Cómo usar

### Compilar y Ejecutar
```bash
# Compilar
javac -cp src src/calculadora/*.java

# Ejecutar
java -cp src calculadora.Calculadora
```

### Operaciones Básicas
1. Haga clic en los números para ingresarlos
2. Seleccione una operación (+, -, *, /)
3. Ingrese el segundo número
4. Presione "=" para obtener el resultado
5. Use "C" para limpiar y empezar una nueva operación

### Funciones del Menú
- **Opciones > Nuevo**: Limpia la calculadora y registra "Nuevo" en la bitácora
- **Opciones > Historial**: Abre una ventana mostrando todas las operaciones guardadas
- **Ayuda > Manual de Usuario**: Muestra instrucciones de uso

## Archivos del Proyecto

### Estructura
```
src/
├── calculadora/
│   ├── Calculadora.java      # Clase principal
│   └── CalculadoraGUI.java    # Interfaz gráfica
bitacoraCalculadora.txt        # Archivo de historial (se crea automáticamente)
```

### Descripción de Archivos
- **Calculadora.java**: Clase principal que inicia la aplicación
- **CalculadoraGUI.java**: Contiene toda la lógica de la interfaz gráfica y cálculos
- **bitacoraCalculadora.txt**: Archivo donde se guardan todas las operaciones con fecha y hora

## Características Técnicas

### Tecnologías Utilizadas
- **Java Swing**: Para la interfaz gráfica
- **Java AWT**: Para el manejo de eventos y layouts
- **Java IO**: Para el manejo de archivos (bitácora)

### Componentes Java Utilizados
- `JFrame`: Ventana principal
- `JTextField`: Campo de texto para mostrar números
- `JButton`: Botones numéricos y de operaciones
- `JMenuBar`, `JMenu`, `JMenuItem`: Sistema de menús
- `GridLayout`, `BorderLayout`: Organización de componentes
- `FileWriter`: Escritura del archivo de bitácora
- `ActionListener`: Manejo de eventos de botones

## Funcionalidades Especiales

### Bitácora Automática
- Todas las operaciones se guardan automáticamente
- Formato: `DD/MM/YYYY HH:MM:SS - operación`
- Ejemplo: `02/08/2025 05:59:12 - 6.0 * 8.0 = 48.0`

### Validaciones
- **División por cero**: Muestra mensaje de error
- **Números decimales**: Solo permite un punto decimal por número
- **Operaciones consecutivas**: Maneja correctamente múltiples operaciones

### Interfaz Amigable
- Botones con tamaño adecuado para fácil uso
- Botón "C" destacado en rojo para fácil identificación
- Campo de texto alineado a la derecha como calculadoras tradicionales
- Ventana no redimensionable para mantener diseño

## Autor
VELA - Universidad Mariano Gálvez

## Notas
- La calculadora maneja números enteros y decimales
- Los resultados se muestran como enteros cuando es posible
- El archivo de bitácora se crea en el directorio de ejecución
- La aplicación es compatible con Java 8 o superior
